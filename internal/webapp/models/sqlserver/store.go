package sqlserver

import (
	"context"
	"pebble/internal/webapp/models"
	appointmentsql "pebble/internal/webapp/models/sqlserver/appointment"
	messagesql "pebble/internal/webapp/models/sqlserver/message"
	paymentsql "pebble/internal/webapp/models/sqlserver/payment"
	reportsql "pebble/internal/webapp/models/sqlserver/report"
	tagsql "pebble/internal/webapp/models/sqlserver/tag"
	"pebble/pkg/udb"

	"gorm.io/gorm"
)

type store struct {
}

func NewStore() models.IStore {
	return &store{}
}

func (s *store) Transaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return udb.GetDb().Transaction(func(tx *gorm.DB) error {
		newCtx := udb.ContextWithDB(ctx, tx)
		return fn(newCtx)
	})
}

func (s *store) Accounts() models.IAccounts {
	return NewAccountTable()
}

func (s *store) Tenants() models.ITenant {
	return NewTenantTable()
}

func (s *store) Locations() models.ILocation {
	return NewLocationTable()
}

func (s *store) TenantMembers() models.ITenantMember {
	return NewTenantMemberTable()
}

func (s *store) Clients() models.IClient {
	return NewClientTable()
}

func (s *store) TwilioAccounts() models.ITwilioAccount {
	return NewTwilioAccountTable()
}

func (s *store) PhoneConfigs() models.IPhoneConfig {
	return NewPhoneConfigTable()
}

func (s *store) MessageRecords() models.IMessageRecord {
	return NewMessageRecordTable()
}

func (s *store) SMSMessages() models.ISMSMessage {
	return NewSMSMessageTable()
}

func (s *store) Taxes() models.ITax {
	return NewTaxTables()
}

func (s *store) ServiceCategories() models.IServiceCategories {
	return NewServiceCategoriesTable()
}

func (s *store) Services() models.IServices {
	return NewServiceTables()
}

func (s *store) ServiceStaff() models.IServiceStaff {
	return NewServiceStaffTable()
}

func (s *store) Appointments() models.IAppointment {
	return appointmentsql.NewAppointmentTable()
}
func (s *store) AppointmentServices() models.IAppointmentService {
	return appointmentsql.NewAppointmentServiceTable()
}

func (s *store) PaymentMethods() models.IPaymentMethods {
	return paymentsql.NewPaymentMethodsTable()
}

func (s *store) PaymentRecords() models.IPaymentRecords {
	return paymentsql.NewPaymentRecordsTable()
}

func (s *store) PaymentItemRecords() models.IPaymentItemRecords {
	return paymentsql.NewPaymentItemRecordsTable()
}

func (s *store) RefundRecords() models.IRefundRecords {
	return paymentsql.NewRefundRecordsTable()
}

func (s *store) RefundItemRecords() models.IRefundItemRecords {
	return paymentsql.NewRefundItemRecordsTable()
}

func (s *store) Holidays() models.IHoliday {
	return NewHolidayTable()
}

func (s *store) StaffSchedules() models.IStaffSchedule {
	return NewStaffScheduleTables()
}

func (s *store) StaffScheduleOverrides() models.IStaffScheduleOverride {
	return NewStaffScheduleOverrideTables()
}

func (s *store) StaffScheduleSettings() models.IStaffScheduleSettings {
	return NewStaffScheduleSettingsTables()
}

func (s *store) Permissions() models.IPermission {
	return NewPermissionTable()
}

func (s *store) Roles() models.IRole {
	return NewRoleTable()
}

func (s *store) RolePermissions() models.IRolePermission {
	return NewRolePermissionTable()
}

func (s *store) TenantMemberRoles() models.ITenantMemberRoles {
	return NewTenantMemberRolesTable()
}

func (s *store) Tags() models.ITag {
	return tagsql.NewTagTable()
}

func (s *store) ClientTags() models.ClientTag {
	return tagsql.NewClientTagTable()
}

func (s *store) AppointmentNote() models.IAppointmentNote {
	return appointmentsql.NewAppointmentNoteTable()
}

func (s *store) AppointmentTip() models.IAppointmentTip {
	return appointmentsql.NewAppointmentTipTable()
}

func (s *store) Report() models.IReports {
	return reportsql.NewReport()
}

func (s *store) MessageTemplates() models.IMessageTemplate {
	return messagesql.NewMessageTemplateTables()
}

func (s *store) NotificationLogs() models.INotificationLog {
	return messagesql.NewNotificationLogTables()
}

func (s *store) AppointmentGuest() models.IAppointmentGuest {
	return appointmentsql.NewAppointmentGuestTable()
}
