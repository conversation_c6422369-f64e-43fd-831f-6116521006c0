package message

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
)

type MessageTemplateTables struct {
	commonsql.BaseTable
}

func NewMessageTemplateTables() *MessageTemplateTables {
	table := &MessageTemplateTables{}
	table.SetTableName("message_templates")
	return table
}

func (t *MessageTemplateTables) Create(actx *auth.Ctx, template *message.MessageTemplate) (*message.MessageTemplate, error) {
	err := t.NoLimitTx(actx.Context()).Create(template).Error
	if err != nil {
		return nil, err
	}
	return template, nil
}

func (t *MessageTemplateTables) Get(actx *auth.Ctx, templateType message.TemplateType) (*message.MessageTemplate, error) {
	var template message.MessageTemplate
	err := t.LimitLocationTx(actx).Where("template_type = ?", templateType).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (t *MessageTemplateTables) List(actx *auth.Ctx, locationID string) ([]*message.MessageTemplate, error) {
	var templates []*message.MessageTemplate

	// Find templates for the specific location OR tenant-wide templates
	query := t.LimitTenantTx(actx).Where("location_id = ? OR location_id = ''", locationID)

	err := query.Find(&templates).Error
	if err != nil {
		return nil, err
	}
	return templates, nil
}

func (t *MessageTemplateTables) Update(actx *auth.Ctx, templateType message.TemplateType, req *message.UpdateMessageTemplateReq) error {
	updates := map[string]interface{}{}
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Subject != nil {
		updates["subject"] = *req.Subject
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.TriggerConfigDays != nil {
		updates["trigger_config_days"] = *req.TriggerConfigDays
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) == 0 {
		return nil // Nothing to update
	}

	return t.LimitLocationTx(actx).
		Model(&message.MessageTemplate{}).
		Where("template_type = ?", templateType).
		Updates(updates).Error
}

// Delete removes a message template from the database.
/*
func (t *MessageTemplateTables) Delete(actx *auth.Ctx, eventType string) error {
	return t.LimitLocationTx(actx).
		Where("event_type = ?", eventType).
		Delete(&message.MessageTemplate{}).Error
}
*/
