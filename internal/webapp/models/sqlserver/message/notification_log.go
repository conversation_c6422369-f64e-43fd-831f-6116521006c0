package message

import (
	"errors"
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"

	"gorm.io/gorm"
)

type NotificationLogTables struct {
	commonsql.BaseTable
}

func NewNotificationLogTables() *NotificationLogTables {
	table := &NotificationLogTables{}
	table.SetTableName("notification_logs")
	return table
}

func (t *NotificationLogTables) Create(actx *auth.Ctx, log *message.NotificationLog) error {
	return t.NoLimitTx(actx.Context()).Create(log).Error
}

func (t *NotificationLogTables) BatchCreate(actx *auth.Ctx, logs []*message.NotificationLog) error {
	if len(logs) == 0 {
		return nil
	}
	return t.NoLimitTx(actx.Context()).CreateInBatches(logs, 100).Error
}

func (t *NotificationLogTables) GetExistingLogTriggerIDs(actx *auth.Ctx, triggerEventIDs []string, templateType message.TemplateType) (map[string]struct{}, error) {
	if len(triggerEventIDs) == 0 {
		return make(map[string]struct{}), nil
	}

	var existingIDs []string
	err := t.NoLimitTx(actx.Context()).
		Model(&message.NotificationLog{}).
		Where("trigger_event_id IN ? AND template_type = ?", triggerEventIDs, templateType).
		Pluck("trigger_event_id", &existingIDs).Error

	if err != nil {
		return nil, err
	}

	existingIDSet := make(map[string]struct{}, len(existingIDs))
	for _, id := range existingIDs {
		existingIDSet[id] = struct{}{}
	}

	return existingIDSet, nil
}

// Exists checks if a notification log entry already exists for a given trigger event and template.
func (s *NotificationLogTables) Exists(actx *auth.Ctx, triggerEventID string, templateType message.TemplateType) (bool, error) {
	var count int64
	err := s.NoLimitTx(actx.Context()).
		Model(&message.NotificationLog{}).
		Where("trigger_event_id = ? AND template_type = ?", triggerEventID, templateType).
		Count(&count).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}

	return count > 0, nil
}
