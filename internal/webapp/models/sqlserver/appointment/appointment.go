package appointmentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type appointmentTable struct {
	commonsql.BaseTable
}

func NewAppointmentTable() *appointmentTable {
	table := &appointmentTable{}
	table.SetTableName("appointments")
	return table
}

func (a *appointmentTable) Create(actx *auth.Ctx, data appointmenttypes.AppointmentItem) (*appointmenttypes.Appointment, error) {
	appointment := appointmenttypes.Appointment{
		AppointmentEntity: appointmenttypes.AppointmentEntity{
			TenantId:           actx.TenantId,
			LocationId:         actx.LocationId,
			AppointmentId:      uid.GenerateAppointmentId(),
			ClientId:           data.ClientId,
			ClientCount:        data.ClientCount,
			Status:             data.Status,
			PaymentStatus:      data.PaymentStatus,
			ScheduledStartDate: data.ScheduledStartDate,
			ScheduledStartTime: data.ScheduledStartTime,
			ScheduledEndDate:   data.ScheduledEndDate,
			ScheduledEndTime:   data.ScheduledEndTime,
			ActualStartDate:    data.ActualStartDate,
			ActualStartTime:    data.ActualStartTime,
			ActualEndDate:      data.ActualEndDate,
			ActualEndTime:      data.ActualEndTime,
			Source:             data.Source,
		},
	}

	err := a.NoLimitTx(actx.Context()).Create(&appointment).Error
	if err != nil {
		return nil, err
	}
	return &appointment, nil
}

func (a *appointmentTable) Get(actx *auth.Ctx, appointmentId string) (*appointmenttypes.Appointment, error) {
	appointment := appointmenttypes.Appointment{}
	err := a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).First(&appointment).Error
	if err != nil {
		return nil, err
	}
	return &appointment, nil
}

func (a *appointmentTable) GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.Appointment, error) {
	if len(appointmentIds) == 0 {
		return []appointmenttypes.Appointment{}, nil
	}
	var resp []appointmenttypes.Appointment
	err := a.LimitLocationTx(actx).Where("appointment_id in (?)", appointmentIds).Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (a *appointmentTable) List(actx *auth.Ctx, where appointmenttypes.GetAppointmentConditions) ([]appointmenttypes.Appointment, int64, error) {
	var appointments []appointmenttypes.Appointment
	db := a.LimitLocationTx(actx)
	if where.ScheduledStartDate != nil {
		db = db.Where("scheduled_start_date >= ?", where.ScheduledStartDate)
	}
	if where.ScheduledEndDate != nil {
		db = db.Where("scheduled_end_date <= ?", where.ScheduledEndDate)
	}
	if where.ScheduledStartTime != nil {
		db = db.Where("scheduled_start_time >= ?", where.ScheduledStartTime)
	}
	if where.ScheduledEndTime != nil {
		db = db.Where("scheduled_end_time <= ?", where.ScheduledEndTime)
	}
	if len(where.StatusList) > 0 {
		db = db.Where("status in (?)", where.StatusList)
	}
	if len(where.PaymentStatusList) > 0 {
		db = db.Where("payment_status in (?)", where.PaymentStatusList)
	}

	if where.TimelineType != nil && *where.TimelineType == commontypes.TimelineTypeFuture &&
		where.CurrentDate != nil {
		db = db.Where("(scheduled_end_date > ? OR (scheduled_end_date = ? AND scheduled_end_time > ?))",
			where.CurrentDate, where.CurrentDate, where.CurrentTime)
	}

	if where.TimelineType != nil && *where.TimelineType == commontypes.TimelineTypeHistory &&
		where.CurrentDate != nil {
		db = db.Where("(scheduled_end_date < ? OR (scheduled_end_date = ? AND scheduled_end_time < ?))",
			where.CurrentDate, where.CurrentDate, where.CurrentTime)
	}

	// if where.TimelineType != nil && *where.TimelineType == appointmenttypes.TimelineTypeCurrent &&
	//     where.CurrentDate != nil {
	//     db = db.Where("(scheduled_start_date < ? OR (scheduled_start_date = ? AND scheduled_start_time <= ?)) AND (scheduled_end_date > ? OR (scheduled_end_date = ? AND scheduled_end_time >= ?))",
	//         where.CurrentDate, where.CurrentDate, where.CurrentTime,
	//         where.CurrentDate, where.CurrentDate, where.CurrentTime)
	// }

	if where.ClientId != nil {
		db = db.Where("client_id = ?", *where.ClientId)
	} else if len(where.ClientIds) > 0 {
		db = db.Where("client_id IN (?)", where.ClientIds)
	}

	var total int64
	countDb := db
	err := countDb.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if where.Page != nil && where.PageSize != nil {
		db = db.Offset((*where.Page - 1) * *where.PageSize).Limit(*where.PageSize)
	}
	db = db.Order("scheduled_start_date ASC, scheduled_start_time ASC")

	err = db.Find(&appointments).Error
	if err != nil {
		return nil, 0, err
	}
	return appointments, total, nil
}

func (a *appointmentTable) ListByDate(actx *auth.Ctx, date string) ([]appointmenttypes.Appointment, error) {
	var appointments []appointmenttypes.Appointment
	db := a.LimitLocationTx(actx)
	db = db.Where("scheduled_start_date <= ? and scheduled_end_date >= ? ", date, date)
	err := db.Find(&appointments).Error
	if err != nil {
		return nil, err
	}
	return appointments, nil
}

func (a *appointmentTable) Update(actx *auth.Ctx, appointmentId string, data appointmenttypes.UpdateAppointmentReq) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Updates(data).Error
}

func (a *appointmentTable) Delete(actx *auth.Ctx, appointmentId string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Delete(&appointmenttypes.Appointment{}).Error
}
