package models

import (
	"pebble/internal/webapp/types"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
)

// IMessageRecord defines the interface for message record management
type IMessageRecord interface {
	// Create creates a new message record
	Create(actx *auth.Ctx, data types.MessageRecordEntity) (*types.MessageRecord, error)

	// GetByMessageID gets message record by message ID
	GetByMessageID(actx *auth.Ctx, messageID string) (*types.MessageRecord, error)

	// GetByClientID gets message records by client ID
	GetByClientID(actx *auth.Ctx, clientID string) ([]types.MessageRecord, error)

	GetMessages(actx *auth.Ctx, params types.QueryMessageParams) ([]types.MessageRecord, error)

	GetMessagesGroupedByClient(actx *auth.Ctx, params types.QueryMessageParams) ([]types.MessageRecord, error)
}

type IMessageTemplate interface {
	Create(actx *auth.Ctx, template *message.MessageTemplate) (*message.MessageTemplate, error)
	Get(actx *auth.Ctx, templateType message.TemplateType) (*message.MessageTemplate, error)
	List(actx *auth.Ctx, locationID string) ([]message.MessageTemplate, error)
	Update(actx *auth.Ctx, templateType message.TemplateType, req *message.UpdateMessageTemplateReq) error
}

type INotificationLog interface {
	Create(actx *auth.Ctx, log *message.NotificationLog) error
	Exists(actx *auth.Ctx, triggerEventID string, templateType message.TemplateType) (bool, error)
	BatchCreate(actx *auth.Ctx, logs []message.NotificationLog) error
	GetExistingLogTriggerIDs(actx *auth.Ctx, triggerEventIDs []string, templateType message.TemplateType) (map[string]struct{}, error)
}
