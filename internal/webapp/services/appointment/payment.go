package appointmentservice

import (
	"context"
	"errors"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/util"
	"time"
)

type appointmentPaymentSvc struct {
	store models.IStore
}

func AppointmentPaymentSvc() *appointmentPaymentSvc {
	return &appointmentPaymentSvc{
		store: sqlserver.NewStore(),
	}
}

func (a *appointmentPaymentSvc) AppointmentPayment(ctx context.Context, appointmentId string, data appointmenttypes.AppointmentPaymentReq) error {
	actx := auth.AuthConText(ctx)
	appointment, err := AppointmentSvc().Get(ctx, appointmentId)
	if udb.IsRecordNotFound(err) {
		return uerror.ErrAppointmentNotFound
	}
	if err != nil {
		return err
	}

	location, err := a.store.Locations().GetLocation(actx)
	if err != nil {
		return err
	}

	var balance appointmenttypes.Bills
	// split payment
	paymentStatus := commontypes.PaymentStatusPaid
	if data.TargetId != "" {
		clientIds, err := AppointmentSvc().InitAppointmentWalkInClients(ctx, appointmentId)
		if err != nil {
			return err
		}
		bills, err := AppointmentSvc().AABills(ctx, location.CurrencySymbol, clientIds, *appointment)
		if err != nil {
			return err
		}
		var found bool
		for _, bill := range bills.Items {
			if bill.ClientId == data.TargetId {
				balance = bill
				found = true
				break
			}
		}
		if !found {
			return errors.New("bill not found")
		}
		if bills.AmountUnpaid <= data.Amount {
			paymentStatus = commontypes.PaymentStatusPaid
		} else {
			paymentStatus = commontypes.PaymentStatusPartialPaid
		}
	} else {
		// full payment
		b, err := AppointmentSvc().Bills(ctx, location.CurrencySymbol, *appointment)
		if err != nil {
			return err
		}
		balance = b
		if b.AmountUnpaid <= data.Amount {
			paymentStatus = commontypes.PaymentStatusPaid
		} else {
			paymentStatus = commontypes.PaymentStatusPartialPaid
		}
	}

	if balance.AmountUnpaid <= 0 {
		return nil
	}
	if balance.AmountUnpaid > data.Amount {
		return errors.New("balance is not enough")
	}
	if balance.AmountFee > data.Fee {
		return errors.New("fee is not match")
	}
	if balance.AmountTax > data.Tax {
		return errors.New("tax is not match")
	}
	if balance.AmountTip > data.Tip {
		return errors.New("tip is not match")
	}

	methods, err := a.store.PaymentMethods().Get(actx, data.MethodId)
	if err != nil {
		return err
	}
	tips, err := a.store.AppointmentTip().GetByAppointmentIds(actx, []string{appointmentId})
	if err != nil {
		return err
	}
	appointmentTipIds := make([]string, 0, len(tips))

	err = a.store.Transaction(ctx, func(ctx context.Context) error {
		actx := auth.AuthConText(ctx)
		err = a.store.Appointments().Update(actx, appointmentId, appointmenttypes.UpdateAppointmentReq{
			PaymentStatus: util.Ptr(paymentStatus),
		})
		if err != nil {
			return err
		}
		payment, err := a.store.PaymentRecords().Create(actx, paymenttypes.CreatePaymentReq{
			ClientId:       appointment.ClientId,
			TargetId:       data.TargetId,
			OrderType:      commontypes.OrderTypeAppointment,
			OrderId:        appointmentId,
			MethodId:       methods.MethodId,
			MethodName:     methods.Name,
			MethodType:     methods.Type,
			AmountTip:      balance.AmountTip,
			AmountTax:      balance.AmountTax,
			AmountFee:      balance.AmountFee,
			AmountSubTotal: balance.AmountSubtotal,
			AmountTotal:    balance.AmountTotal,
			FeePayer:       commontypes.FeePayerCustomer, // default is customer
			Status:         commontypes.PaymentStatusPaid,
			Currency:       location.Currency,
			CaptureAt:      util.Ptr(time.Now().UTC()),
		})
		if err != nil {
			return err
		}

		itemRecords := make([]paymenttypes.CreatePaymentItemReq, 0, len(balance.Services)+len(balance.Tips))
		for _, s := range balance.Services {
			itemRecords = append(itemRecords, paymenttypes.CreatePaymentItemReq{
				ClientId:       appointment.ClientId,
				AmountSubTotal: s.Price,
				AmountTax:      s.Tax,
				AmountTotal:    s.Price + s.Tax,
				OrderId:        appointmentId,
				OrderItemType:  commontypes.OrderTypeService,
				OrderType:      commontypes.OrderTypeAppointment,
				OrderItemId:    s.ApptServiceId,
				PaymentId:      payment.PaymentId,
				TaxInclusive:   commontypes.TaxInclusiveNo,
				Status:         commontypes.PaymentStatusPaid,
				AccountId:      s.AccountId,
				Currency:       location.Currency,
				TargetType:     s.TargetType,
				TargetId:       s.TargetId,
				CaptureAt:      util.Ptr(time.Now().UTC()),
			})
		}

		for _, tip := range tips {
			appointmentTipIds = append(appointmentTipIds, tip.ApptTipId)
			itemRecords = append(itemRecords, paymenttypes.CreatePaymentItemReq{
				ClientId:       tip.ClientId,
				AmountTotal:    tip.AmountTip,
				OrderId:        appointmentId,
				OrderItemType:  commontypes.OrdertypeTip,
				OrderType:      commontypes.OrderTypeAppointment,
				OrderItemId:    tip.ApptTipId,
				PaymentId:      payment.PaymentId,
				TaxInclusive:   commontypes.TaxInclusiveNo,
				Status:         commontypes.PaymentStatusPaid,
				AccountId:      tip.AccountId,
				Currency:       location.Currency,
				TargetType:     commontypes.TargetTypeClient,
				TargetId:       tip.ClientId,
				CaptureAt:      util.Ptr(time.Now().UTC()),
				AmountSubTotal: tip.AmountTip,
				ReferenceId:    "",
			})
		}
		err = a.store.AppointmentTip().BatchUpdatePaymentStatus(actx, appointmentId, appointmentTipIds, commontypes.PaymentStatusPaid)
		if err != nil {
			return err
		}

		_, err = a.store.PaymentItemRecords().BatchCreate(actx, itemRecords)
		return err
	})
	if err != nil {
		return err
	}

	return nil
}

func (p *appointmentPaymentSvc) QueryPaymentByAppointmentId(ctx context.Context, appointmentId string) ([]paymenttypes.PaymentRecordDetails, error) {
	records, err := p.store.PaymentRecords().GetByOrderIds(auth.AuthConText(ctx), []string{appointmentId})
	if err != nil {
		return nil, err
	}
	items, err := p.store.PaymentItemRecords().GetByOrderIds(auth.AuthConText(ctx), []string{appointmentId})
	if err != nil {
		return nil, err
	}

	itemRecordMap := make(map[string][]paymenttypes.PaymentItemRecords)
	for _, item := range items {
		itemRecordMap[item.PaymentId] = append(itemRecordMap[item.PaymentId], item)
	}

	resp := make([]paymenttypes.PaymentRecordDetails, 0, len(records))
	for _, record := range records {
		resp = append(resp, paymenttypes.PaymentRecordDetails{
			PaymentRecords: record,
			Items:          util.NoNilSlice(itemRecordMap[record.PaymentId]),
		})
	}

	return resp, nil
}

func (p *appointmentPaymentSvc) QueryPaymentByAppointmentIds(ctx context.Context, appointmentIds []string) (map[string][]paymenttypes.PaymentRecordDetails, error) {
	records, err := p.store.PaymentRecords().GetByOrderIds(auth.AuthConText(ctx), appointmentIds)
	if err != nil {
		return nil, err
	}
	items, err := p.store.PaymentItemRecords().GetByOrderIds(auth.AuthConText(ctx), appointmentIds)
	if err != nil {
		return nil, err
	}

	key := func(appintmentId, paymentId string) string {
		return fmt.Sprintf("%s-%s", appintmentId, paymentId)
	}
	itemRecordMap := make(map[string][]paymenttypes.PaymentItemRecords)
	for _, item := range items {
		k := key(item.OrderId, item.PaymentId)
		itemRecordMap[k] = append(itemRecordMap[k], item)
	}

	recordMap := make(map[string][]paymenttypes.PaymentRecordDetails)
	for _, record := range records {
		k := key(record.OrderId, record.PaymentId)
		recordMap[record.OrderId] = append(recordMap[record.OrderId], paymenttypes.PaymentRecordDetails{
			PaymentRecords: record,
			Items:          util.NoNilSlice(itemRecordMap[k]),
		})
	}

	return recordMap, nil
}
