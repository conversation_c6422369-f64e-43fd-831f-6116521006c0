package workers

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
	"pebble/pkg/template"
	"pebble/pkg/udb"
	"pebble/pkg/ulog"
	"pebble/pkg/util"
	"sync"
	"time"

	"go.uber.org/zap"
)

// AppointmentReminderService is responsible for sending appointment reminders.
type AppointmentReminderService interface {
	TriggerReminders(ctx context.Context) error
}

type appointmentReminderSvc struct {
	store    models.IStore
	smsVider sms.IProvider
	renderer template.TemplateRenderer
}

// NewAppointmentReminderService creates a new reminder service.
func NewAppointmentReminderService(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) AppointmentReminderService {
	return &appointmentReminderSvc{
		store:    store,
		smsVider: smsVider,
		renderer: renderer,
	}
}

// TriggerReminders fetches and processes appointment reminders.
func (s *appointmentReminderSvc) TriggerReminders(ctx context.Context) error {
	locations, err := s.store.Locations().AdminGetAllLocations(ctx)
	if err != nil {
		ulog.Error(ctx, "failed to get all locations", zap.Error(err))
		return err
	}

	for _, location := range locations {
		if location.Status != commontypes.StatusActive {
			continue // Skip inactive locations
		}

		actx := auth.AuthConText(auth.ContextWithExt(ctx, auth.Ext{
			TenantId:   location.TenantId,
			LocationId: location.LocationId,
		}))

		ulog.Info(ctx, "processing reminders for location", zap.String("locationID", location.LocationId), zap.String("tenantID", location.TenantId))

		template, err := s.store.MessageTemplates().Get(actx, message.AppointmentReminder)
		if err != nil {
			if udb.IsRecordNotFound(err) {
				ulog.Warn(ctx, "no appointment reminder template found, skipping", zap.String("locationID", location.LocationId))
			} else {
				ulog.Error(ctx, "failed to get message template", zap.String("locationID", location.LocationId), zap.Error(err))
			}
			continue
		}

		if template.Status != commontypes.StatusActive {
			ulog.Info(ctx, "appointment reminder template is not active, skipping", zap.String("locationID", location.LocationId))
			continue
		}

		if err := s.processTemplate(ctx, actx, template, location); err != nil {
			ulog.Error(ctx, "failed to process template",
				zap.String("locationID", location.LocationId),
				zap.String("templateType", string(template.TemplateType)),
				zap.Error(err))
			// Continue to next location
		}
	}

	return nil
}

func (s *appointmentReminderSvc) processTemplate(ctx context.Context, actx *auth.Ctx, template *message.MessageTemplate, location types.Location) error {
	// Load location's timezone
	loc, err := time.LoadLocation(location.Timezone)
	if err != nil {
		ulog.Error(ctx, "failed to load location timezone", zap.String("timezone", location.Timezone), zap.Error(err))
		// Fallback to UTC if timezone is invalid
		loc = time.UTC
	}

	// Get current time in location's timezone
	nowInLocationTime := time.Now().In(loc)

	// trigger_config_days is negative for "before event"
	reminderDate := nowInLocationTime.AddDate(0, 0, -template.TriggerConfigDays)

	// Define a time window, e.g., 1 hour, to catch appointments
	// scheduled within this period. This assumes the worker runs hourly.
	startTime := reminderDate.Truncate(time.Hour)
	endTime := startTime.Add(time.Hour - 1*time.Second)

	starTimeInt := int64(startTime.Hour()*3600 + startTime.Minute()*60 + startTime.Second())
	endTimeInt := int64(endTime.Hour()*3600 + endTime.Minute()*60 + endTime.Second())
	conditions := appointmenttypes.GetAppointmentConditions{
		ScheduledStartDate: util.Ptr(startTime.Format("2006-01-02")),
		ScheduledEndDate:   util.Ptr(endTime.Format("2006-01-02")),
		ScheduledStartTime: &starTimeInt,
		ScheduledEndTime:   &endTimeInt,
		StatusList: []commontypes.AppointmentStatusType{
			commontypes.AppointmentStatusUnconfirm,
			commontypes.AppointmentStatusWaitListen,
			commontypes.AppointmentStatusConfirm,
		},
	}
	appointments, _, err := s.store.Appointments().List(actx, conditions)
	if err != nil {
		ulog.Error(ctx, "failed to list appointments for reminder", zap.Any("conditions", conditions), zap.Error(err))
		return err
	}

	if len(appointments) == 0 {
		return nil // No appointments to remind for this template in this window
	}

	// Step 1: Batch fetch client details
	clientIDs := make([]string, 0, len(appointments))
	appointmentIDs := make([]string, 0, len(appointments))
	for _, appt := range appointments {
		clientIDs = append(clientIDs, appt.ClientId)
		appointmentIDs = append(appointmentIDs, appt.AppointmentId)
	}

	clients, err := s.store.Clients().GetClientByIds(actx, clientIDs)
	if err != nil {
		ulog.Error(ctx, "failed to get clients by ids", zap.Strings("clientIDs", clientIDs), zap.Error(err))
		return err
	}
	clientMap := make(map[string]types.Client)
	for _, c := range clients {
		clientMap[c.ClientId] = c
	}

	// Step 2: Batch check for existing notifications
	existingLogs, err := s.store.NotificationLogs().GetExistingLogTriggerIDs(actx, appointmentIDs, template.TemplateType)
	if err != nil {
		ulog.Error(ctx, "failed to check for existing notification logs", zap.Error(err))
		return err
	}

	// Step 3: Filter out appointments that already have notifications or missing client info
	var appointmentsToNotify []appointmenttypes.Appointment
	for _, appt := range appointments {
		if _, exists := existingLogs[appt.AppointmentId]; exists {
			continue // Already sent
		}
		client, ok := clientMap[appt.ClientId]
		if !ok || client.PhoneNumber == "" {
			continue // Skip if client not found or has no phone number
		}
		appointmentsToNotify = append(appointmentsToNotify, appt)
	}

	if len(appointmentsToNotify) == 0 {
		return nil // All appointments for this window have been processed
	}

	// Step 4: Concurrently send reminders and collect logs for batch creation
	var wg sync.WaitGroup
	var successfulLogs []*message.NotificationLog
	var logMutex sync.Mutex

	for _, appt := range appointmentsToNotify {
		wg.Add(1)
		go func(appointment appointmenttypes.Appointment) {
			defer wg.Done()

			client := clientMap[appointment.ClientId]

			// Prepare variables for rendering
			variables := map[string]string{
				"client_name":      client.FirstName,
				"client_full_name": fmt.Sprintf("%s %s", client.FirstName, client.LastName),
				"appointment_date": appointment.ScheduledStartDate,
				"appointment_time": time.Unix(appointment.ScheduledStartTime, 0).Format("3:04 PM"),
			}

			// Render message content
			msgContent, err := s.renderer.Render(template.Content, variables)
			if err != nil {
				ulog.Error(ctx, "failed to render reminder template",
					zap.String("templateType", string(template.TemplateType)),
					zap.String("appointmentID", appointment.AppointmentId),
					zap.Error(err))
				return
			}

			// Send SMS
			smsParams := sms.SendMessageParams{
				To:      client.PhoneNumber,
				Content: msgContent,
			}
			result, err := s.smsVider.SendMessage(ctx, smsParams)
			if err != nil {
				ulog.Error(ctx, "failed to send reminder sms",
					zap.String("appointmentID", appointment.AppointmentId),
					zap.Error(err))
				return
			}

			// If successful, prepare log entry
			logEntry := &message.NotificationLog{
				TenantID:        actx.TenantId,
				LocationID:      actx.LocationId,
				TriggerEventID:  appointment.AppointmentId,
				TemplateType:    template.TemplateType,
				ChannelType:     template.ChannelType,
				MessageRecordID: result.MessageID,
			}

			logMutex.Lock()
			successfulLogs = append(successfulLogs, logEntry)
			logMutex.Unlock()

		}(appt)
	}

	wg.Wait()

	// Step 5: Batch create notification logs for all successful sends
	if len(successfulLogs) > 0 {
		if err := s.store.NotificationLogs().BatchCreate(actx, successfulLogs); err != nil {
			ulog.Error(ctx, "failed to batch create notification logs", zap.Error(err))
			// Even if logging fails, the reminders were sent.
			// Depending on requirements, could implement a retry mechanism here.
		}
	}

	return nil
}
