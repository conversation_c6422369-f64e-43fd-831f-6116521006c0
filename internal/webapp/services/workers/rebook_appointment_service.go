package workers

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/services/sms"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
	"pebble/pkg/template"
	"pebble/pkg/ulog"
	"time"

	"go.uber.org/zap"
)

// RebookAppointmentService is responsible for sending rebook appointment messages.
type RebookAppointmentService interface {
	TriggerRebooks(ctx context.Context) error
}

type rebookAppointmentSvc struct {
	store    models.IStore
	smsVider sms.IProvider
	renderer template.TemplateRenderer
}

// NewRebookAppointmentService creates a new rebook service.
func NewRebookAppointmentService(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) RebookAppointmentService {
	return &rebookAppointmentSvc{
		store:    store,
		smsVider: smsVider,
		renderer: renderer,
	}
}

// TriggerRebooks fetches and processes rebook appointment rules.
func (s *rebookAppointmentSvc) TriggerRebooks(ctx context.Context) error {
	actx := &auth.Ctx{} // Simplified for this task

	templates, err := s.store.MessageTemplates().List(actx, "")
	if err != nil {
		ulog.Error(ctx, "failed to list message templates for rebook", zap.Error(err))
		return err
	}

	for _, template := range templates {
		if template.Status != commontypes.StatusActive ||
			template.TemplateType != message.RebookAppointment ||
			template.ChannelType != "sms" {
			continue
		}

		if err := s.processRebookTemplate(ctx, actx, template); err != nil {
			ulog.Error(ctx, "failed to process rebook template", zap.String("templateType", string(template.TemplateType)), zap.Error(err))
		}
	}
	return nil
}

func (s *rebookAppointmentSvc) processRebookTemplate(ctx context.Context, actx *auth.Ctx, template *message.MessageTemplate) error {
	now := time.Now()
	// For rebooks, TriggerConfigDays is positive, representing "days since last visit".
	rebookDate := now.AddDate(0, 0, -template.TriggerConfigDays)
	rebookDateStr := rebookDate.Format("2006-01-02")

	// Step 1: Find all appointments that occurred on the rebook date.
	conditions := appointmenttypes.GetAppointmentConditions{
		ScheduledStartDate: &rebookDateStr,
		ScheduledEndDate:   &rebookDateStr,
	}
	appointmentsOnDate, _, err := s.store.Appointments().List(actx, conditions)
	if err != nil {
		ulog.Error(ctx, "failed to list appointments for rebook date", zap.String("date", rebookDateStr), zap.Error(err))
		return err
	}

	if len(appointmentsOnDate) == 0 {
		return nil
	}

	// Get unique client IDs from these appointments.
	clientIDsOnDate := make(map[string]bool)
	for _, appt := range appointmentsOnDate {
		clientIDsOnDate[appt.ClientId] = true
	}

	// Step 2 & 3: For each client, check if this was their last appointment.
	for clientID := range clientIDsOnDate {
		futureDate := rebookDate.AddDate(0, 0, 1).Format("2006-01-02")
		futureConditions := appointmenttypes.GetAppointmentConditions{
			ClientId:           &clientID,
			ScheduledStartDate: &futureDate, // Any appointment after the rebook date
		}
		futureAppointments, _, err := s.store.Appointments().List(actx, futureConditions)
		if err != nil {
			ulog.Warn(ctx, "failed to check for future appointments", zap.String("clientID", clientID), zap.Error(err))
			continue // Skip this client on error
		}

		// If there are no future appointments, this client is eligible for rebook.
		if len(futureAppointments) == 0 {
			s.sendRebookToClient(ctx, actx, clientID, template)
		}
	}

	return nil
}

func (s *rebookAppointmentSvc) sendRebookToClient(ctx context.Context, actx *auth.Ctx, clientID string, template *message.MessageTemplate) {
	// Check if a rebook was already sent for this client and template.
	exists, err := s.store.NotificationLogs().Exists(actx, clientID, template.TemplateType)
	if err != nil {
		ulog.Error(ctx, "failed to check notification log for rebook", zap.Error(err))
		return
	}
	if exists {
		return // Already sent
	}

	// Get client details
	client, err := s.store.Clients().GetClientById(actx, clientID)
	if err != nil {
		ulog.Error(ctx, "failed to get client details for rebook", zap.String("clientID", clientID), zap.Error(err))
		return
	}

	if client.PhoneNumber == "" {
		return // No phone number to send to
	}

	// Prepare variables for rendering
	variables := map[string]string{
		"client_name":         client.FirstName,
		"client_first_name":   client.FirstName,
		"client_last_name":    client.LastName,
		"client_full_name":    client.FirstName + " " + client.LastName,
		"client_email":        client.Email,
		"client_phone_number": client.PhoneNumber,
	}

	// Send SMS
	msgContent, err := s.renderer.Render(template.Content, variables)
	if err != nil {
		ulog.Error(ctx, "failed to render rebook template", zap.String("templateType", string(template.TemplateType)), zap.Error(err))
		return
	}

	smsParams := sms.SendMessageParams{
		To:      client.PhoneNumber,
		Content: msgContent,
	}

	result, err := s.smsVider.SendMessage(ctx, smsParams)
	if err != nil {
		ulog.Error(ctx, "failed to send rebook sms", zap.Error(err))
		return
	}

	// Log the notification
	logEntry := &message.NotificationLog{
		TenantID:        actx.TenantId,
		LocationID:      actx.LocationId,
		TriggerEventID:  clientID, // For rebook, the event ID is the client ID
		TemplateType:    template.TemplateType,
		ChannelType:     template.ChannelType,
		MessageRecordID: result.MessageID,
	}
	if err := s.store.NotificationLogs().Create(actx, logEntry); err != nil {
		ulog.Error(ctx, "failed to create rebook notification log", zap.Error(err))
	}
}
