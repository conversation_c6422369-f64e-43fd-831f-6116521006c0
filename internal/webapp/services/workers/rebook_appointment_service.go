package workers

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	messageSvc "pebble/internal/webapp/services/message"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
	"pebble/pkg/template"
	"pebble/pkg/ulog"
	"pebble/pkg/util"
	"pebble/pkg/utime"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// RebookAppointmentService is responsible for sending rebook appointment messages.
type RebookAppointmentService interface {
	TriggerRebooks(ctx context.Context) error
}

type rebookAppointmentSvc struct {
	store    models.IStore
	smsVider sms.IProvider
	renderer template.TemplateRenderer
}

// NewRebookAppointmentService creates a new rebook service.
func NewRebookAppointmentService(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) RebookAppointmentService {
	return &rebookAppointmentSvc{
		store:    store,
		smsVider: smsVider,
		renderer: renderer,
	}
}

// TriggerRebooks fetches and processes rebook appointment rules.
func (s *rebookAppointmentSvc) TriggerRebooks(ctx context.Context) error {
	locations, err := s.store.Locations().AdminGetAllLocations(ctx)
	if err != nil {
		ulog.Error(ctx, "failed to get all locations", zap.Error(err))
		return err
	}
	for _, location := range locations {
		if location.Status != commontypes.StatusActive {
			continue // Skip inactive locations
		}

		ctx := auth.ContextWithExt(ctx, auth.Ext{
			TenantId:   location.TenantId,
			LocationId: location.LocationId,
		})

		ulog.Info(ctx, "processing reminders for location", zap.String("locationID", location.LocationId), zap.String("tenantID", location.TenantId))

		template, err := messageSvc.NewMessageTemplateService().Get(ctx, message.AppointmentReminder)
		if err != nil {
			ulog.Error(ctx, "failed to get message template", zap.String("locationID", location.LocationId), zap.Error(err))
			continue
		}

		if template.Status != commontypes.StatusActive {
			ulog.Info(ctx, "appointment reminder template is not active, skipping", zap.String("locationID", location.LocationId))
			continue
		}

		if err := s.processRebookTemplate(ctx, template, location); err != nil {
			ulog.Error(ctx, "failed to process rebook template", zap.String("templateType", string(template.TemplateType)), zap.Error(err))
		}

	}

	return nil
}

func (s *rebookAppointmentSvc) GetTriggerEventId(clientId, appointmentId string) string {
	return fmt.Sprintf("%s_%s", clientId, appointmentId)
}

func (s *rebookAppointmentSvc) GetTriggerEventID(ctx context.Context) error {
	return nil
}

func (s *rebookAppointmentSvc) processRebookTemplate(ctx context.Context, template *message.MessageTemplate, location types.Location) error {
	actx := auth.AuthConText(ctx)
	// Load location's timezone
	appts, err := s.GetNeedToNotificationsAppt(ctx, location.Timezone, template)
	if err != nil {
		ulog.Error(ctx, "failed to get need to notifications appt", zap.Error(err))
		return err
	}

	clientIds := make([]string, 0, len(appts))
	for _, appt := range appts {
		clientIds = append(clientIds, appt.ClientId)
	}

	clients, err := s.store.Clients().GetClientByIds(actx, clientIds)
	if err != nil {
		ulog.Error(ctx, "failed to get clients details for rebook", zap.Error(err))
		return err
	}

	var clientMap = make(map[string]types.Client)
	for _, client := range clients {
		clientMap[client.ClientId] = client
	}

	var wg sync.WaitGroup
	var successfulLogs []message.NotificationLog
	var logMutex sync.Mutex

	for _, appt := range appts {
		client, ok := clientMap[appt.ClientId]
		if !ok {
			continue
		}
		if client.PhoneNumber == "" {
			continue
		}

		appointment := appt
		wg.Add(1)
		util.GoSafe(actx.Context(), func(ctx context.Context) {
			defer wg.Done()

			// Send message
			// Prepare variables for rendering
			dateTime, err := utime.ParseDateTime(appointment.ScheduledStartDate, appointment.ScheduledStartTime, location.Timezone)
			if err != nil {
				ulog.Error(ctx, "failed to parse appointment date and time", zap.Error(err))
				return
			}
			variables := map[string]string{
				"client_first_name":   client.FirstName,
				"client_last_name":    client.LastName,
				"client_full_name":    client.FirstName + " " + client.LastName,
				"client_email":        client.Email,
				"client_phone_number": client.PhoneNumber,
				"appointment_date":    dateTime.Format(time.DateOnly),
				"appointment_time":    dateTime.Format("3:04 PM"),
			}

			// Send SMS
			msgContent, err := s.renderer.Render(template.Content, variables)
			if err != nil {
				ulog.Error(ctx, "failed to render rebook template", zap.String("templateType", string(template.TemplateType)), zap.Error(err))
				return
			}

			smsParams := sms.SendMessageParams{
				ClientId:       client.ClientId,
				To:             client.PhoneNumber,
				Content:        msgContent,
				TemplateID:     string(template.TemplateType),
				TemplateParams: variables,
			}

			result, err := s.sendSMSWithImmediateRetry(ctx, smsParams, appointment.AppointmentId)
			if err != nil {
				ulog.Error(ctx, "failed to send rebook sms after retries", zap.Error(err))
				return
			}

			// Log the notification
			logEntry := message.NotificationLog{
				TenantID:        actx.TenantId,
				LocationID:      actx.LocationId,
				TriggerEventID:  s.GetTriggerEventId(client.ClientId, appointment.AppointmentId),
				TemplateType:    template.TemplateType,
				ChannelType:     template.ChannelType,
				MessageRecordID: result.MessageID,
			}

			logMutex.Lock()
			successfulLogs = append(successfulLogs, logEntry)
			logMutex.Unlock()
		})
	}

	wg.Wait()

	// Step 5: Batch create notification logs for all successful sends
	if len(successfulLogs) > 0 {
		if err := s.store.NotificationLogs().BatchCreate(actx, successfulLogs); err != nil {
			ulog.Error(ctx, "failed to batch create notification logs", zap.Error(err))
			// Even if logging fails, the reminders were sent.
			// Depending on requirements, could implement a retry mechanism here.
		}
	}

	return nil
}

func (s *rebookAppointmentSvc) GetNeedToNotificationsAppt(ctx context.Context, timezone string, template *message.MessageTemplate) ([]appointmenttypes.Appointment, error) {
	actx := auth.AuthConText(ctx)
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		ulog.Error(ctx, "failed to load location timezone", zap.String("timezone", timezone), zap.Error(err))
		// Fallback to UTC if timezone is invalid
		loc = time.UTC
	}

	// Get current time in location's timezone
	nowInLocationTime := time.Now().In(loc)
	// For rebooks, TriggerConfigDays is positive, representing "days since last visit".
	rebookDate := nowInLocationTime.Add(-time.Duration(template.TriggerConfigHours) * time.Hour)
	rebookDateStr := rebookDate.Format("2006-01-02")

	// Step 1: Find the latest appointment for each client.
	latestAppointments, err := s.store.Appointments().ListLatestForEachClient(actx)
	if err != nil {
		ulog.Error(ctx, "failed to list latest appointments for each client", zap.Error(err))
		return nil, err
	}

	// Step 2: Filter for clients whose last appointment was on the rebookDate.
	var triggerIds []string
	var rebookAppts []appointmenttypes.Appointment
	for _, appt := range latestAppointments {
		if appt.ScheduledStartDate == rebookDateStr {
			triggerIds = append(triggerIds, s.GetTriggerEventId(appt.ClientId, appt.AppointmentId))
			rebookAppts = append(rebookAppts, appt)
		}
	}

	existingLogs, err := s.store.NotificationLogs().GetExistingLogTriggerIDs(actx, triggerIds, template.TemplateType)
	if err != nil {
		ulog.Error(ctx, "failed to check for existing notification logs", zap.Error(err))
		return nil, err
	}

	var needToNotifications []appointmenttypes.Appointment
	for _, appt := range rebookAppts {
		_, ok := existingLogs[s.GetTriggerEventId(appt.ClientId, appt.AppointmentId)]
		if !ok {
			needToNotifications = append(needToNotifications, appt)
		}
	}

	return needToNotifications, nil
}

// sendSMSWithImmediateRetry sends SMS with immediate retry mechanism
func (s *rebookAppointmentSvc) sendSMSWithImmediateRetry(ctx context.Context, params sms.SendMessageParams, appointmentID string) (*sms.SendMessageResult, error) {
	const maxImmediateRetries = 3
	const retryDelay = 1 * time.Second

	var lastErr error

	for attempt := 0; attempt < maxImmediateRetries; attempt++ {
		if attempt > 0 {
			ulog.Info(ctx, "retrying SMS send",
				zap.String("appointmentID", appointmentID),
				zap.Int("attempt", attempt+1))
			time.Sleep(retryDelay * time.Duration(attempt))
		}

		result, err := s.smsVider.SendMessage(ctx, params)
		if err == nil {
			if attempt > 0 {
				ulog.Info(ctx, "SMS send succeeded after retry",
					zap.String("appointmentID", appointmentID),
					zap.Int("attempt", attempt+1))
			}
			return result, nil
		}

		lastErr = err

		// Check if error is retryable (network errors, temporary failures)
		if !s.isRetryableError(err) {
			ulog.Info(ctx, "SMS send error is not retryable",
				zap.String("appointmentID", appointmentID),
				zap.Error(err))
			break
		}
	}

	return nil, fmt.Errorf("failed to send SMS after %d attempts: %w", maxImmediateRetries, lastErr)
}

// isRetryableError checks if an error is worth retrying immediately
func (s *rebookAppointmentSvc) isRetryableError(err error) bool {
	// Check for network errors, timeout errors, temporary service errors
	errStr := err.Error()
	retryablePatterns := []string{
		"timeout",
		"connection",
		"network",
		"temporary",
		"service unavailable",
		"rate limit",
	}

	for _, pattern := range retryablePatterns {
		if strings.Contains(strings.ToLower(errStr), pattern) {
			return true
		}
	}

	return false
}
