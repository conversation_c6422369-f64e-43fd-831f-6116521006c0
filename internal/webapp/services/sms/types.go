package sms

import (
	"context"
	"pebble/internal/webapp/types"
	"pebble/internal/webapp/types/message"
	"time"
)

type PurchaseNumberReq struct {
	PhoneNumber string `json:"phone_number" binding:"required" example:"+15551234567"`
	Country     string `json:"country" binding:"required" example:"US"`
	AreaCode    int    `json:"area_code" example:"555"`
}

type PhoneNumber struct {
	PhoneNumber string                  `json:"phone_number" example:"+15551234567"`
	Capability  []types.PhoneCapability `json:"capability" example:"[\"SMS\",\"Voice\"]"`
	Country     string                  `json:"country" example:"US"`
}

type SendMessageParams struct {
	ClientId string `example:"client_123456"`
	// From    string
	To      string `example:"+15551234567"`
	Content string `example:"Your appointment is confirmed for tomorrow at 2pm."`

	TemplateID     string            `example:"tmpl_123456"`
	TemplateParams map[string]string `example:"{\"name\":\"John\",\"time\":\"2pm\"}"`
}

type Message struct {
	From    string `example:"+15557654321"`
	To      string `example:"+15551234567"`
	Content string `example:"Your appointment is confirmed for tomorrow at 2pm."`

	Status string `example:"delivered"`
}

type SearchAvailabeNumbersReq struct {
	Country  string `form:"country" json:"country" binding:"required" example:"US"`
	AreaCode int    `form:"area_code" json:"area_code" example:"555"`
}

type SendMessageResult struct {
	MessageID string `json:"message_id"`
}

// RetryConfig holds configuration for retry behavior
type RetryConfig struct {
	MaxRetries          int           // Maximum number of retry attempts
	InitialDelay        time.Duration // Initial delay for first retry
	MaxDelay            time.Duration // Maximum delay between retries
	BackoffMultiplier   float64       // Multiplier for exponential backoff
	RetryableErrorCodes []string      // List of error codes that are retryable
}

// DefaultRetryConfig returns the default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:        5,
		InitialDelay:      1 * time.Minute,
		MaxDelay:          60 * time.Minute,
		BackoffMultiplier: 2.0,
		RetryableErrorCodes: []string{
			"30001", // Queue overflow
			"30002", // Account suspended
			"30003", // Unreachable destination handset
			"30004", // Message blocked
			"30005", // Unknown destination handset
			"30006", // Landline or unreachable carrier
			"30007", // Carrier violation
			"30008", // Unknown error
			"30009", // Missing segment
			"30010", // Message price exceeds max price
		},
	}
}

// RetryService defines the interface for SMS retry operations
type RetryService interface {
	// ScheduleRetry schedules a retry for a failed message
	ScheduleRetry(ctx context.Context, messageID string, errorCode string) error

	// ExecuteRetry executes a retry for a scheduled message
	ExecuteRetry(ctx context.Context, messageID string) error

	// ProcessScheduledRetries processes all scheduled retries that are due
	ProcessScheduledRetries(ctx context.Context) error

	// GetRetryableMessages gets messages that are eligible for retry
	GetRetryableMessages(ctx context.Context, beforeTime time.Time) ([]types.SMSMessage, error)

	// SyncNotificationStatus syncs SMS status to notification logs
	SyncNotificationStatus(ctx context.Context, messageID string, status message.NotificationStatus) error

	// CanRetry checks if a message can be retried
	CanRetry(msg *types.SMSMessage, errorCode string) bool

	// CalculateNextRetryTime calculates the next retry time based on retry count
	CalculateNextRetryTime(retryCount int) time.Time
}
