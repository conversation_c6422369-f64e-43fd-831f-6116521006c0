package message

import (
	"context"

	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"

	"go.uber.org/zap"
)

type IMessageTemplateService interface {
	Get(ctx context.Context, templateType message.TemplateType) (*message.MessageTemplate, error)
	List(ctx context.Context) ([]*message.MessageTemplate, error)
	Update(ctx context.Context, templateType message.TemplateType, req *message.UpdateMessageTemplateReq) error
	GetNavigation(ctx context.Context) ([]*message.NavigationCategory, error)
}

type service struct {
	store models.IStore
}

func NewMessageTemplateService() IMessageTemplateService {
	return &service{
		store: sqlserver.NewStore(),
	}
}

func (s *service) Get(ctx context.Context, templateType message.TemplateType) (*message.MessageTemplate, error) {
	actx := auth.AuthConText(ctx)
	template, err := s.store.MessageTemplates().Get(actx, templateType)
	if err != nil {
		if udb.IsRecordNotFound(err) {
			// Return default template if not found
			defaultData, ok := message.DefaultTemplatesMap[string(templateType)]
			if !ok {
				return nil, uerror.New(uerror.ErrCodeInvalidParam, "no default template defined for this event type")
			}
			return &message.MessageTemplate{
				TenantID:          actx.TenantId,
				LocationID:        actx.LocationId,
				TemplateType:      templateType,
				Name:              defaultData.Name,
				Subject:           defaultData.Subject,
				Content:           defaultData.Content,
				TriggerConfigDays: defaultData.TriggerConfigDays,
				Status:            commontypes.StatusActive, // Default to active
			}, nil
		}
		ulog.Error(ctx, "failed to get message template", zap.String("templateType", string(templateType)), zap.Error(err))
		return nil, err
	}
	return template, nil
}

func (s *service) List(ctx context.Context) ([]*message.MessageTemplate, error) {
	actx := auth.AuthConText(ctx)
	// For now, we use locationID from the context.
	templates, err := s.store.MessageTemplates().List(actx, actx.LocationId)
	if err != nil {
		ulog.Error(ctx, "failed to list message templates", zap.Error(err))
		return nil, err
	}
	return templates, nil
}

func (s *service) Update(ctx context.Context, templateType message.TemplateType, req *message.UpdateMessageTemplateReq) error {
	actx := auth.AuthConText(ctx)

	if req.Content != nil {
		if err := ValidateTemplateContent(string(templateType), *req.Content); err != nil {
			return err
		}
	}

	_, err := s.store.MessageTemplates().Get(actx, templateType)
	if err != nil {
		if udb.IsRecordNotFound(err) {
			// Create if not exists
			defaultData := message.DefaultTemplatesMap[string(templateType)]
			newTemplate := &message.MessageTemplate{
				TenantID:          actx.TenantId,
				LocationID:        actx.LocationId,
				TemplateType:      templateType,
				Name:              defaultData.Name,
				Subject:           defaultData.Subject,
				Content:           defaultData.Content,
				TriggerConfigDays: defaultData.TriggerConfigDays,
				Status:            commontypes.StatusActive,
			}
			if req.Name != nil {
				newTemplate.Name = *req.Name
			}
			if req.Subject != nil {
				newTemplate.Subject = *req.Subject
			}
			if req.Content != nil {
				newTemplate.Content = *req.Content
			}
			if req.TriggerConfigDays != nil {
				newTemplate.TriggerConfigDays = *req.TriggerConfigDays
			}
			if req.Status != nil {
				newTemplate.Status = commontypes.Status(*req.Status)
			}
			_, createErr := s.store.MessageTemplates().Create(actx, newTemplate)
			return createErr
		}
		return err
	}

	// Update if exists
	err = s.store.MessageTemplates().Update(actx, templateType, req)
	if err != nil {
		ulog.Error(ctx, "failed to update message template", zap.String("templateType", string(templateType)), zap.Error(err))
		return err
	}
	return nil
}

func (s *service) GetNavigation(ctx context.Context) ([]*message.NavigationCategory, error) {
	// For now, the navigation is static. We can make it more dynamic later if needed.
	navigation := []*message.NavigationCategory{
		{
			Category: "Appointment Management",
			Items: []message.NavigationEvent{
				{Name: "Appointment Reminder", TemplateType: message.AppointmentReminder},
				{Name: "Rebook Appointment", TemplateType: message.RebookAppointment},
			},
		},
	}
	return navigation, nil
}
