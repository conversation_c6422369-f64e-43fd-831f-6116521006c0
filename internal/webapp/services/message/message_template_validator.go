package message

import (
	"fmt"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/uerror"
	"regexp"
)

var (
	// templateVariableRegexp is a regular expression to find all {{...}} placeholders.
	templateVariableRegexp = regexp.MustCompile(`\{\{([a-zA-Z0-9_]+)\}\}`)
)

// ValidateTemplateContent checks if the variables used in the template content are supported for the given template type.
func ValidateTemplateContent(templateType message.TemplateType, content string) error {
	supportedVars, ok := message.SupportedVariablesMap[templateType]
	if !ok {
		// This should ideally not happen if templateType is validated before,
		// but it's a good safeguard.
		return uerror.New(uerror.ErrCodeInvalidParam, fmt.Sprintf("template type '%s' has no supported variables defined", templateType))
	}

	matches := templateVariableRegexp.FindAllStringSubmatch(content, -1)
	if matches == nil {
		return nil // No variables used, so it's valid.
	}

	// Create a set for quick lookup of supported variables.
	supportedSet := make(map[string]struct{}, len(supportedVars))
	for _, v := range supportedVars {
		supportedSet[v.Variable] = struct{}{}
	}

	// var unsupportedVars []string
	for _, match := range matches {
		if len(match) > 1 {
			variableName := match[1]
			if _, supported := supportedSet[variableName]; !supported {
				return fmt.Errorf("unsupported variable: %s", variableName)
				// unsupportedVars = append(unsupportedVars, variableName)
			}
		}
	}

	// if len(unsupportedVars) > 0 {
	// 	return uerror.New(uerror.ErrCodeInvalidParam, fmt.Sprintf("unsupported variables used: [%s]. Supported variables for '%s' are: [%s]",
	// 		strings.Join(unsupportedVars, ", "),
	// 		templateType,
	// 		strings.Join(message.GetFormattedSupportedVariables(templateType), ", "),
	// 	))
	// }

	return nil
}
