package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/ulog"
)

type locationSvc struct {
	store models.IStore
}

func LocationSvc() *locationSvc {
	return &locationSvc{
		store: sqlserver.NewStore(),
	}
}

func (l *locationSvc) QueryLocation(ctx context.Context) (*types.Location, error) {
	loaction, err := l.store.Locations().GetLocation(auth.AuthConText(ctx))
	if err != nil {
		ulog.Errorln(ctx, "query location error:", err)
		return nil, err
	}
	return loaction, nil
}

func (l *locationSvc) UpdateLocation(ctx context.Context, data types.UpdateLocationReq) error {
	err := l.store.Locations().UpdateLocation(auth.AuthConText(ctx), data)
	if err != nil {
		ulog.Errorln(ctx, "update location error:", err)
		return err
	}
	return nil
}
