package webapp

import (
	"context"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"pebble/internal/webapp/config"
	"pebble/pkg/mandrill"
	"pebble/pkg/udb"
	"pebble/pkg/ugin/validator"
	"pebble/pkg/ujwt"
	"pebble/pkg/ulog"
	"pebble/pkg/utwilio"
	"strconv"
	"syscall"
	"time"
)

func Init() {
	// Initialize configuration
	c := config.Init()

	// Initialize components with config
	ulog.Init(&c.Log)
	defer ulog.Close()

	ujwt.InitJWTService(&c.JWT)
	udb.InitDB(&c.Database)
	utwilio.Init(&c.Twilio)
	mandrill.Init(&c.Mandrill)

	validator.Init()
}

// todo 后续用Google/wire重构为依赖注入形式

func Run() {
	server := &http.Server{
		Handler:      router(),
		ReadTimeout:  300 * time.Second,
		WriteTimeout: 300 * time.Second,
		Addr:         net.JoinHostPort("0.0.0.0", strconv.Itoa(config.C.App.Port)),
	}
	server.SetKeepAlivesEnabled(false)

	go func() {
		log.Printf("%s starting %s server on %s", config.C.App.Name, config.C.App.Mode, server.Addr)
		err := server.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			log.Fatalf("webapp run error: %v", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down webapp...")

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Webapp forced to shutdown: %s", err)
	}

	log.Println("Webapp exited gracefully")
}
