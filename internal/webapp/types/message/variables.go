package message

// SupportedVariablesMap maps template types to their supported template variables.
var SupportedVariablesMap = map[string][]string{
	"appointment_reminder": {
		"client_name",
		"client_full_name",
		"appointment_date",
		"appointment_time",
	},
	"rebook_appointment": {
		"client_name",
		"client_first_name",
		"client_last_name",
		"client_full_name",
		"client_email",
		"client_phone_number",
	},
}

// GetFormattedSupportedVariables returns the list of supported variables for a given template type,
// formatted as "{{variable_name}}".
func GetFormattedSupportedVariables(templateType string) []string {
	vars, ok := SupportedVariablesMap[templateType]
	if !ok {
		return nil
	}

	formattedVars := make([]string, len(vars))
	for i, v := range vars {
		formattedVars[i] = "{{" + v + "}}"
	}
	return formattedVars
}
