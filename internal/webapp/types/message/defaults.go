package message

// DefaultMessageTemplate defines the default values for a message template.
type DefaultMessageTemplate struct {
	Name              string
	Subject           string
	Content           string
	TriggerConfigDays int
}

// DefaultTemplatesMap holds the default data for each event type.
var DefaultTemplatesMap = map[string]DefaultMessageTemplate{
	"appointment_reminder": {
		Name:              "Appointment Reminder",
		Subject:           "Your Appointment Reminder",
		Content:           "Hi {{client_name}}, this is a reminder for your appointment on {{appointment_date}} at {{appointment_time}}.",
		TriggerConfigDays: -1, // e.g., 1 day before
	},
	"rebook_appointment": {
		Name:              "Rebook Appointment",
		Subject:           "It's Time to Rebook Your Appointment",
		Content:           "Hi {{client_name}}, it's been a while! We miss you. It's time to book your next appointment with us.",
		TriggerConfigDays: 30, // e.g., 30 days after last appointment
	},
}
