package message

import (
	commontypes "pebble/internal/webapp/types/common"
	"time"
)

// NotificationStatus represents the status of a notification
// @enum.1=Pending status, notification is waiting to be sent
// @enum.2=Sent status, notification has been sent to provider
// @enum.3=Delivered status, notification was successfully delivered
// @enum.4=Failed status, notification failed to deliver
type NotificationStatus int8

const (
	NotificationStatusPending   NotificationStatus = 1 // Pending status, notification is waiting to be sent
	NotificationStatusSent      NotificationStatus = 2 // Sent status, notification has been sent to provider
	NotificationStatusDelivered NotificationStatus = 3 // Delivered status, notification was successfully delivered
	NotificationStatusFailed    NotificationStatus = 4 // Failed status, notification failed to deliver
)

// NotificationLog represents a log entry for a sent notification.
type NotificationLog struct {
	TenantID        string       `gorm:"column:tenant_id;type:varchar(64);not null;index" json:"tenant_id"`
	LocationID      string       `gorm:"column:location_id;type:varchar(64);not null;default:''" json:"location_id"`
	TriggerEventID  string       `gorm:"column:trigger_event_id;type:varchar(64);not null" json:"trigger_event_id"`
	TemplateType    TemplateType `gorm:"column:template_type;type:varchar(50);not null" json:"template_type"`
	ChannelType     string       `gorm:"column:channel_type;type:varchar(20);not null" json:"channel_type"`
	MessageRecordID string       `gorm:"column:message_record_id;type:varchar(64)" json:"message_record_id"`
	SentAt          time.Time    `gorm:"column:sent_at;autoCreateTime" json:"sent_at"`

	// Status tracking fields
	Status       NotificationStatus `gorm:"column:status;default:1" json:"status"`
	RetryCount   int                `gorm:"column:retry_count;default:0" json:"retry_count"`
	LastRetryAt  *time.Time         `gorm:"column:last_retry_at" json:"last_retry_at"`
	ErrorMessage string             `gorm:"column:error_message;type:text" json:"error_message"`

	commontypes.Model
}
