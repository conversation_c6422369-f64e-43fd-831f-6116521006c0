package message

import (
	"time"

	"gorm.io/gorm"
)

// NotificationLog represents a log entry for a sent notification.
type NotificationLog struct {
	ID              int64          `gorm:"primaryKey" json:"id"`
	TenantID        string         `gorm:"column:tenant_id;type:varchar(64);not null;index" json:"tenant_id"`
	LocationID      string         `gorm:"column:location_id;type:varchar(64);not null;default:''" json:"location_id"`
	TriggerEventID  string         `gorm:"column:trigger_event_id;type:varchar(64);not null" json:"trigger_event_id"`
	TemplateType    TemplateType   `gorm:"column:template_type;type:varchar(50);not null" json:"template_type"`
	ChannelType     string         `gorm:"column:channel_type;type:varchar(20);not null" json:"channel_type"`
	MessageRecordID string         `gorm:"column:message_record_id;type:varchar(64)" json:"message_record_id"`
	SentAt          time.Time      `gorm:"column:sent_at;autoCreateTime" json:"sent_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName sets the table name for the NotificationLog model.
func (NotificationLog) TableName() string {
	return "notification_logs"
}
