package message

import (
	commontypes "pebble/internal/webapp/types/common"

	"gorm.io/gorm"
)

// TemplateType defines the type of a message template.
type TemplateType string

const (
	// AppointmentReminder is for appointment reminders.
	AppointmentReminder TemplateType = "appointment_reminder"
	// RebookAppointment is for rebooking reminders.
	RebookAppointment TemplateType = "rebook_appointment"
)

// MessageTemplate is the core model for the message_templates table.
type MessageTemplate struct {
	TenantID           string             `gorm:"column:tenant_id;type:varchar(64);not null;uniqueIndex:idx_tenant_location_template_type" json:"tenant_id"`
	LocationID         string             `gorm:"column:location_id;type:varchar(64);not null;default:'';uniqueIndex:idx_tenant_location_template_type" json:"location_id"`
	Name               string             `gorm:"column:name;type:varchar(100);not null" json:"name"`
	TemplateType       TemplateType       `gorm:"column:template_type;type:varchar(50);not null;uniqueIndex:idx_tenant_location_template_type" json:"template_type"`
	ChannelType        string             `gorm:"column:channel_type;type:varchar(20);not null;default:'sms'" json:"channel_type"`
	Subject            string             `gorm:"column:subject;type:varchar(255)" json:"subject"`
	Content            string             `gorm:"column:content;type:text;not null" json:"content"`
	TriggerConfigDays  int                `gorm:"column:trigger_config_days;not null" json:"trigger_config_days"`
	Status             commontypes.Status `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	SupportedVariables []string           `json:"supported_variables,omitempty" gorm:"-"`
	commontypes.Model
}

func (t *MessageTemplate) AfterFind(tx *gorm.DB) (err error) {
	t.SupportedVariables = GetFormattedSupportedVariables(string(t.TemplateType))
	return
}

// CreateMessageTemplateReq defines the request body for creating a new message template.
type CreateMessageTemplateReq struct {
	Name              string       `json:"name" binding:"required,max=100"`
	TemplateType      TemplateType `json:"template_type" binding:"required,oneof=appointment_reminder rebook_appointment"`
	ChannelType       string       `json:"channel_type" binding:"required,oneof=sms email"`
	Subject           string       `json:"subject,omitempty"`
	Content           string       `json:"content" binding:"required"`
	TriggerConfigDays int          `json:"trigger_config_days" binding:"required,min=0"`
}

// UpdateMessageTemplateReq defines the request body for updating an existing message template.
type UpdateMessageTemplateReq struct {
	Name              *string             `json:"name,omitempty" binding:"omitempty,max=100"`
	Subject           *string             `json:"subject,omitempty" binding:"omitempty,max=255"`
	Content           *string             `json:"content,omitempty" binding:"omitempty,max=65535"`
	TriggerConfigDays *int                `json:"trigger_config_days,omitempty" binding:"omitempty,min=0"`
	Status            *commontypes.Status `json:"status,omitempty" binding:"omitempty,oneof=1 2"`
}

func (m *MessageTemplate) TableName() string {
	return "message_templates"
}
