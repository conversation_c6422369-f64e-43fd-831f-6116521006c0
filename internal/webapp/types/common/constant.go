package commontypes

// Status represents the active state of a record
// @enum.1=Active status, record is currently active and in use
// @enum.2=Inactive status, record is currently not active
type Status int8

const (
	StatusActive   Status = 1 // Active status, record is currently active and in use
	StatusInactive Status = 2 // Inactive status, record is currently not active
)

const DefaultPageSize = 20

// TargetType represents the recipient type of a service
// @enum.1=Client type, service is targeted at the primary client
// @enum.2=Sub-client type, service is targeted at a secondary client
// @enum.3=Pet type, service is targeted at a pet
type TargetType int8

const (
	TargetTypeClient    TargetType = 1 // Client type, service is targeted at the primary client
	TargetTypeSubClient TargetType = 2 // Sub-client type, service is targeted at a secondary client
	TargetTypePet       TargetType = 3 // Pet type, service is targeted at a pet
	// TargetTypeWalkIn    TargetType = 4 // Walk-in type, service is targeted at a walk-in
)

// OrderType represents the source of a payment
// @enum.1=Appointment source, payment is for an appointment
// @enum.2=Sale source, payment is for a product sale
type OrderType int8

const (
	OrderTypeAppointment OrderType = 1 // Appointment source, payment is for an appointment
	OrderTypeSale        OrderType = 2 // Sale source, payment is for a product sale
	OrderTypeService     OrderType = 3
	OrderTypeProduct     OrderType = 4
	OrdertypeTip         OrderType = 5
)

type FeePayer int8

const (
	FeePayerCustomer FeePayer = 1
	FeePayerMerchant FeePayer = 2
)

type TaxInclusive int8

const (
	TaxInclusiveYes TaxInclusive = 1
	TaxInclusiveNo  TaxInclusive = 2
)

// PaymentRecordType represents the type of payment record
// @enum.1=Payment type, record of a payment transaction
// @enum.2=Refund type, record of a refund transaction
type PaymentRecordType int8

const (
	TypePayment PaymentRecordType = 1 // Payment type, record of a payment transaction
	TypeRefund  PaymentRecordType = 2 // Refund type, record of a refund transaction
)

// PaymentRecordStatus represents the status of a payment record
// @enum.1=Pending status, payment is being processed
// @enum.2=Success status, payment was successful
// @enum.3=Refunded status, payment has been refunded
// @enum.4=Failed status, payment has failed
type PaymentRecordStatus int8

const (
	PaymentStatusUnpaid        PaymentRecordStatus = 1 // Pending status, payment is being processed
	PaymentStatusPaid          PaymentRecordStatus = 2 // Success status, payment was successful
	PaymentStatusPartialPaid   PaymentRecordStatus = 3 // Partial Paid status, payment was partially paid
	PaymentStatusRefund        PaymentRecordStatus = 4 // Refunded status, payment has been refunded
	PaymentStatusPartialRefund PaymentRecordStatus = 5 // Partial Refund status, payment has been partially refunded
	PaymentStatusFailed        PaymentRecordStatus = 6 // Failed status, payment has failed
)

func (s PaymentRecordStatus) String() string {
	switch s {
	case PaymentStatusUnpaid:
		return "Unpaid"
	case PaymentStatusPaid:
		return "Paid"
	case PaymentStatusPartialPaid:
		return "Partially Paid"
	case PaymentStatusRefund:
		return "Refunded"
	case PaymentStatusPartialRefund:
		return "Partially Refunded"
	case PaymentStatusFailed:
		return "Failed"
	default:
		return "Unknown"
	}
}

// PaymentMethodTypes represents the type of payment method
// @enum.1=Cash payment method, physical cash payment
// @enum.2=Gift Card payment method, payment using a gift card
// @enum.3=Online Payment method, electronic payment methods
type PaymentMethodTypes int

// PaymentMethodStatus represents the status of a payment method
// @enum.1=Enabled status, payment method is available for use
// @enum.2=Disabled status, payment method is not available for use
type PaymentMethodStatus int

const (
	PaymentMethodCash          PaymentMethodTypes = 1 // Cash payment method, physical cash payment
	PaymentMethodGiftCard      PaymentMethodTypes = 2 // Gift Card payment method, payment using a gift card
	PaymentMethodOnlinePayment PaymentMethodTypes = 3 // Online Payment method, electronic payment methods
)

const (
	PaymentMethodStatusEnabled  PaymentMethodStatus = 1 // Enabled status, payment method is available for use
	PaymentMethodStatusDisabled PaymentMethodStatus = 2 // Disabled status, payment method is not available for use
)

const (
	AppointmentSourceWalkIn = "walk_in"
)

// AppointmentStatusType represents the current status of an appointment
// @enum.1=Unconfirmed status, appointment has not been confirmed yet
// @enum.2=Waiting for response status, waiting for client to respond to confirmation call
// @enum.3=Confirmed status, appointment has been confirmed
// @enum.4=Arrived status, client has arrived at the location
// @enum.5=In Service status, service is currently being provided
// @enum.6=Completed status, service has been completed
// @enum.7=Cancelled status, appointment has been cancelled
// @enum.8=No Show status, client did not show up for the appointment
type AppointmentStatusType int8

const (
	AppointmentStatusUnconfirm  AppointmentStatusType = 1 // Unconfirmed status, appointment has not been confirmed yet
	AppointmentStatusWaitListen AppointmentStatusType = 2 // Waiting for response status, waiting for client to respond to confirmation call
	AppointmentStatusConfirm    AppointmentStatusType = 3 // Confirmed status, appointment has been confirmed
	AppointmentStatusArrived    AppointmentStatusType = 4 // Arrived status, client has arrived at the location
	AppointmentStatusInService  AppointmentStatusType = 5 // In Service status, service is currently being provided
	AppointmentStatusCompleted  AppointmentStatusType = 6 // Completed status, service has been completed
	AppointmentStatusCancelled  AppointmentStatusType = 7 // Cancelled status, appointment has been cancelled
	AppointmentStatusNoShow     AppointmentStatusType = 8 // No Show status, client did not show up for the appointment
)

func (s AppointmentStatusType) String() string {
	switch s {
	case AppointmentStatusUnconfirm:
		return "Unconfirmed"
	case AppointmentStatusWaitListen:
		return "Waiting for response"
	case AppointmentStatusConfirm:
		return "Confirmed"
	case AppointmentStatusArrived:
		return "Arrived"
	case AppointmentStatusInService:
		return "In Service"
	case AppointmentStatusCompleted:
		return "Completed"
	case AppointmentStatusCancelled:
		return "Cancelled"
	case AppointmentStatusNoShow:
		return "No Show"
	default:
		return "Unknown"
	}
}

// PaymentStatus represents the payment status of an appointment
// @enum.1=Unpaid status, appointment fees have not been paid
// @enum.2=Partially paid status, appointment fees have been partially paid
// @enum.3=Fully paid status, appointment fees have been fully paid
// type PaymentStatus int8

// const (
// 	PaymentStatusUnpaid                 PaymentStatus = 1 // Unpaid status, appointment fees have not been paid
// 	PaymentStatusPaid                   PaymentStatus = 2 // Fully paid status, appointment fees have been fully paid
// 	AppointmentPaymentStatusPartialPaid PaymentStatus = 3 // Partially paid status, appointment fees have been partially paid
// )

// func (s PaymentStatus) String() string {
// 	switch s {
// 	case PaymentStatusUnpaid:
// 		return "Unpaid"
// 	case PaymentStatusPaid:
// 		return "Paid"
// 	case AppointmentPaymentStatusPartialPaid:
// 		return "Partially Paid"
// 	default:
// 		return "Unknown"
// 	}
// }

type TimelineType string

const (
	TimelineTypeFuture  TimelineType = "future"
	TimelineTypeHistory TimelineType = "history"
)
