package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// SendMessage godoc
//
//	@Summary		Send SMS message
//	@Description	Send an SMS message to a client
//	@Tags			connections
//	@Accept			json
//	@Produce		json
//	@Param			request	body		types.SendMessageReq	true	"Message information" example({"client_id":"client_123","to":"+12345678901","content":"Your appointment is confirmed"})
//	@Success		200		{object}	writer.Resp{data=object{}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		401		{object}	writer.ErrResp "Unauthorized"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/connections/messages [post]
//	@Security		ApiKeyAuth
func SendMessage(c *gin.Context) {
	var req types.SendMessageReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	ctx := c.Request.Context()
	err := services.MsgSvc().SendMessage(ctx, sms.SendMessageParams{
		ClientId: req.ClientId,
		To:       req.To,
		Content:  req.Content,
	})
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// PurchaseNumber godoc
//
// @Summary      Purchase a phone number
// @Description  Purchase a new phone number for SMS messaging
// @Tags         connections
// @Accept       json
// @Produce      json
// @Param        request  body      sms.PurchaseNumberReq  true  "Purchase parameters" example({"phone_number":"+15551234567","country":"US","area_code":555})
// @Success      200      {object}  writer.Resp{data=object{obj=sms.PhoneNumber}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /connections/phone-numbers [post]
// @Security     ApiKeyAuth
func PurchaseNumber(c *gin.Context) {
	var req sms.PurchaseNumberReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	ctx := c.Request.Context()
	phone, err := services.MsgSvc().PurchaseNumber(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(phone))
}

// ReleaseNumber godoc
//
//	@Summary		Release a phone number
//	@Description	Release a previously purchased phone number
//	@Tags			connections
//	@Accept			json
//	@Produce		json
//	@Param			request	body		object	true	"Phone number to release" example({"phone_number":"+12345678901"})
//	@Success		200		{object}	writer.Resp{data=object{}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		401		{object}	writer.ErrResp "Unauthorized"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/connections/phone-numbers [delete]
//	@Security		ApiKeyAuth
func ReleaseNumber(c *gin.Context) {
	var req struct {
		PhoneNumber string `json:"phone_number" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	ctx := c.Request.Context()
	err := services.MsgSvc().ReleaseNumber(ctx, req.PhoneNumber)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// SearchAvailabeNumbers godoc
//
//	@Summary		Search for available phone numbers
//	@Description	Search for available phone numbers in a specific country and area code
//	@Tags			connections
//	@Accept			json
//	@Produce		json
//	@Param			country		query		string	true	"Country code (e.g., US)" example("US")
//	@Param			area_code	query		int		false	"Area code (e.g., 555)" example(555)
//	@Success		200			{object}	writer.Resp{data=object{list=[]sms.PhoneNumber}} "OK"
//	@Failure		400			{object}	writer.ErrResp "Bad Request"
//	@Failure		401			{object}	writer.ErrResp "Unauthorized"
//	@Failure		500			{object}	writer.ErrResp "Internal Server Error"
//	@Router			/connections/phone-numbers [get]
//	@Security		ApiKeyAuth
func SearchAvailabeNumbers(c *gin.Context) {
	var req sms.SearchAvailabeNumbersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	ctx := c.Request.Context()
	numbers, err := services.MsgSvc().SearchAvailabeNumbers(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(numbers))
}

func QueryMessagesGroupedByClient(c *gin.Context) {
	var params types.QueryMessageParams
	if err := c.ShouldBindQuery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.MsgSvc().QueryMessagesGroupedByClient(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

func QueryClientMessages(c *gin.Context) {
	var uri struct {
		ClientId string `uri:"client_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}
	var params types.QueryClientMessagesParams
	if err := c.ShouldBindQuery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.MsgSvc().QueryClientMessages(c.Request.Context(), uri.ClientId, params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))

}
