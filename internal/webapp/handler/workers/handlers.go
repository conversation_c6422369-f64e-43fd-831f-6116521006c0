package workers

import (
	"context"
	"pebble/internal/webapp/models/sqlserver"
	twilioservice "pebble/internal/webapp/services/sms/twilio-service"
	"pebble/internal/webapp/services/workers"
	"pebble/pkg/template"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ulog"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TriggerAppointmentReminders godoc
// @Summary      Trigger appointment reminders
// @Description  Manually trigger the worker to send appointment reminders.
// @Tags         workers
// @Accept       json
// @Produce      json
// @Success      202      {object}  writer.Resp{data=object{}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /internal/workers/trigger-appointment-reminders [post]
// @Security     InternalSecret
func TriggerAppointmentReminders(c *gin.Context) {
	go func() {
		ctx := context.Background()
		ulog.Info(ctx, "starting appointment reminder job")

		// In a real production environment, these dependencies would be injected
		// through a dependency injection container. For this internal endpoint,
		// we instantiate them directly.
		store := sqlserver.NewStore()
		smsProvider := twilioservice.NewTwilioProvider()
		renderer := template.NewSimpleTemplateRenderer()
		reminderSvc := workers.NewAppointmentReminderService(store, smsProvider, renderer)

		if err := reminderSvc.TriggerReminders(ctx); err != nil {
			ulog.Error(ctx, "appointment reminder job failed", zap.Error(err))
		}
		ulog.Info(ctx, "appointment reminder job finished")
	}()

	writer.ResponseOk(c, writer.Empty())
}

// TriggerRebookAppointments godoc
// @Summary      Trigger rebook appointments
// @Description  Manually trigger the worker to send rebook appointment messages.
// @Tags         workers
// @Accept       json
// @Produce      json
// @Success      202      {object}  writer.Resp{data=object{}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /internal/workers/trigger-rebook-appointments [post]
// @Security     InternalSecret
func TriggerRebookAppointments(c *gin.Context) {
	go func() {
		ctx := context.Background()
		ulog.Info(ctx, "starting rebook appointment job")

		// In a real production environment, these dependencies would be injected
		// through a dependency injection container. For this internal endpoint,
		// we instantiate them directly.
		store := sqlserver.NewStore()
		smsProvider := twilioservice.NewTwilioProvider()
		renderer := template.NewSimpleTemplateRenderer()
		rebookSvc := workers.NewRebookAppointmentService(store, smsProvider, renderer)

		if err := rebookSvc.TriggerRebooks(ctx); err != nil {
			ulog.Error(ctx, "rebook appointment job failed", zap.Error(err))
		}
		ulog.Info(ctx, "rebook appointment job finished")
	}()

	writer.ResponseOk(c, writer.Empty())
}
