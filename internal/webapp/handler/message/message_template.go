package message

import (
	svc "pebble/internal/webapp/services/message"
	messagetypes "pebble/internal/webapp/types/message"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// GetMessageTemplate godoc
// @Summary      Get a message template by TemplateType
// @Description  Get details of a specific message template. If not customized, a default template is returned.
// @Tags         messages
// @Accept       json
// @Produce      json
// @Param        template_type   path      string  true  "Template Type"
// @Success      200      {object}  writer.Resp{data=object{obj=messagetypes.MessageTemplate}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      404      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /message-templates/{template_type} [get]
// @Security     ApiKeyAuth
func GetMessageTemplate(c *gin.Context) {
	var uri struct {
		TemplateType messagetypes.TemplateType `uri:"template_type"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	service := svc.NewMessageTemplateService()
	resp, err := service.Get(c.Request.Context(), uri.TemplateType)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// ListMessageTemplates godoc
// @Summary      List all message templates
// @Description  Get a list of all message templates for the current location. Returns customized templates only.
// @Tags         messages
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{list=[]messagetypes.MessageTemplate}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /message-templates [get]
// @Security     ApiKeyAuth
func ListMessageTemplates(c *gin.Context) {
	service := svc.NewMessageTemplateService()
	resp, err := service.List(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// UpdateMessageTemplate godoc
// @Summary      Update a message template
// @Description  Update an existing message template. If the template does not exist, it will be created.
// @Tags         messages
// @Accept       json
// @Produce      json
// @Param        template_type   path      string  true  "Template Type"
// @Param        request  body      messagetypes.UpdateMessageTemplateReq  true  "Updated Message Template Information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      404      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /message-templates/{template_type} [put]
// @Security     ApiKeyAuth
func UpdateMessageTemplate(c *gin.Context) {
	var uri struct {
		TemplateType string `uri:"template_type"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req messagetypes.UpdateMessageTemplateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	service := svc.NewMessageTemplateService()
	err := service.Update(c.Request.Context(), messagetypes.TemplateType(uri.TemplateType), &req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// GetMessageTemplateNavigation godoc
// @Summary      Get message template navigation
// @Description  Get a structured list of template types for navigation
// @Tags         messages
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{list=[]messagetypes.NavigationCategory}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /message-templates/navigation [get]
// @Security     ApiKeyAuth
func GetMessageTemplateNavigation(c *gin.Context) {
	service := svc.NewMessageTemplateService()
	resp, err := service.GetNavigation(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}
