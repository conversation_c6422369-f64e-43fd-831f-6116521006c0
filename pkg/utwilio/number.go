package utwilio

import (
	"fmt"
	"pebble/pkg/uid"

	"github.com/twilio/twilio-go"

	openapi "github.com/twilio/twilio-go/rest/api/v2010"
)

type SearchAvailabeNumbersReq struct {
	SID      string
	Token    string
	Country  string
	AreaCode int
}

type SearchAvailabeNumbersResp struct {
	PhoneNumber  string
	SmsEnabled   bool
	VoiceEnabled bool
	IsoCountry   string
}

// func SearchAvailabeNumbers(req SearchAvailabeNumbersReq) ([]SearchAvailabeNumbersResp, error) {
// 	client := twilio.NewRestClientWithParams(twilio.ClientParams{
// 		Username: req.SID,
// 		Password: req.Token,
// 	})

// 	searchParams := &openapi.ListAvailablePhoneNumberLocalParams{}
// 	searchParams.SetSmsEnabled(true)
// 	searchParams.SetVoiceEnabled(true)
// 	if req.AreaCode != 0 {
// 		searchParams.SetAreaCode(req.AreaCode)
// 	}

// 	available, err := client.Api.ListAvailablePhoneNumberLocal(req.Country, searchParams)
// 	if err != nil || len(available) == 0 {
// 		return nil, fmt.Errorf("search available phone number err:%w", err)
// 	}

// 	list := make([]SearchAvailabeNumbersResp, 0, len(available))

// 	for _, number := range available {
// 		list = append(list, SearchAvailabeNumbersResp{
// 			PhoneNumber:  *number.PhoneNumber,
// 			SmsEnabled:   number.Capabilities.Sms,
// 			VoiceEnabled: number.Capabilities.Voice,
// 			IsoCountry:   *number.IsoCountry,
// 		})
// 	}

// 	return list, nil
// }

func SearchAvailabeNumbers(req SearchAvailabeNumbersReq) ([]SearchAvailabeNumbersResp, error) {

	return []SearchAvailabeNumbersResp{
		{
			PhoneNumber:  "+8615602069258",
			SmsEnabled:   true,
			VoiceEnabled: true,
			IsoCountry:   "US",
		},
	}, nil
}

type BuyNumberReq struct {
	SID             string
	Token           string
	PhoneNumber     string
	SmsWebhookUrl   string
	VoiceWebhookUrl string
}

type BuyNumberResp struct {
	PhoneNumber  string
	Sid          string
	SmsEnabled   bool
	VoiceEnabled bool
}

// func BuyNumber(req BuyNumberReq) (*BuyNumberResp, error) {
// 	client := twilio.NewRestClientWithParams(twilio.ClientParams{
// 		Username: req.SID,
// 		Password: req.Token,
// 	})

// 	buyParams := &openapi.CreateIncomingPhoneNumberParams{}
// 	buyParams.SetPhoneNumber(req.PhoneNumber)
// 	// buyParams.SetSmsMethod("POST")
// 	// buyParams.SetSmsUrl(req.SmsWebhookUrl)
// 	buyParams.SetVoiceMethod("POST")
// 	buyParams.SetVoiceUrl(req.VoiceWebhookUrl)

// 	number, err := client.Api.CreateIncomingPhoneNumber(buyParams)
// 	if err != nil {
// 		return nil, fmt.Errorf("buy phone number err: %w", err)
// 	}

// 	return &BuyNumberResp{
// 		PhoneNumber:  *number.PhoneNumber,
// 		Sid:          *number.Sid,
// 		SmsEnabled:   number.Capabilities.Sms,
// 		VoiceEnabled: number.Capabilities.Voice,
// 	}, nil
// }

// todo remove this
func BuyNumber(req BuyNumberReq) (*BuyNumberResp, error) {
	// client := twilio.NewRestClientWithParams(twilio.ClientParams{
	// 	Username: req.SID,
	// 	Password: req.Token,
	// })

	// buyParams := &openapi.CreateIncomingPhoneNumberParams{}
	// buyParams.SetPhoneNumber(req.PhoneNumber)
	// // buyParams.SetSmsMethod("POST")
	// // buyParams.SetSmsUrl(req.SmsWebhookUrl)
	// buyParams.SetVoiceMethod("POST")
	// buyParams.SetVoiceUrl(req.VoiceWebhookUrl)

	// number, err := client.Api.CreateIncomingPhoneNumber(buyParams)
	// if err != nil {
	// 	return nil, fmt.Errorf("buy phone number err: %w", err)
	// }

	return &BuyNumberResp{
		PhoneNumber:  req.PhoneNumber,
		Sid:          uid.GenerateUidWithPrefix("simulate-NumberSid"),
		SmsEnabled:   true,
		VoiceEnabled: true,
	}, nil
}

// func DeleteNumber(accountSID, token, sid string) error {
// 	client := twilio.NewRestClientWithParams(twilio.ClientParams{
// 		Username: accountSID,
// 		Password: token,
// 	})

// 	params := &openapi.DeleteIncomingPhoneNumberParams{}
// 	err := client.Api.DeleteIncomingPhoneNumber(sid, params)
// 	if err != nil {
// 		return fmt.Errorf("delete phone number err: %w", err)
// 	}

// 	return nil
// }

func DeleteNumber(accountSID, token, sid string) error {
	return nil
}

type GetNumberResp struct {
	Sid          string
	PhoneNumber  string
	SmsEnabled   bool
	VoiceEnabled bool
}

func GetNumber(accountSID, token, sid string) (*GetNumberResp, error) {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: accountSID,
		Password: token,
	})

	params := &openapi.FetchIncomingPhoneNumberParams{}
	number, err := client.Api.FetchIncomingPhoneNumber(sid, params)
	if err != nil {
		return nil, fmt.Errorf("get phone number info err: %w", err)
	}

	return &GetNumberResp{
		Sid:          *number.Sid,
		PhoneNumber:  *number.PhoneNumber,
		SmsEnabled:   number.Capabilities.Sms,
		VoiceEnabled: number.Capabilities.Voice,
	}, nil
}
