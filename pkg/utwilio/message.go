package utwilio

import (
	"fmt"
	"pebble/pkg/uid"

	"github.com/twilio/twilio-go"
)

type SendMessageReq struct {
	Sid   string
	Token string

	To      string
	Content string

	From                string // From or MessagingServiceSid
	MessagingServiceSid string // From or MessagingServiceSid
}

type Message struct {
	MessageSid string
	Status     string

	From    string
	To      string
	Content string

	ErrorMessage string
	ErrorCode    int
}

// func SendMessage(req SendMessageReq) (*Message, error) {
// 	client := twilio.NewRestClientWithParams(twilio.ClientParams{
// 		Username: req.Sid,
// 		Password: req.Token,
// 	})

// 	params := &openapi.CreateMessageParams{}
// 	params.SetTo(req.To)
// 	// params.SetFrom(req.From)
// 	params.SetBody(req.Content)
// 	params.SetMessagingServiceSid(req.MessagingServiceSid)

// 	resp, err := client.Api.CreateMessage(params)
// 	if err != nil {
// 		return nil, fmt.Errorf("send message err: %w", err)
// 	}

// 	return &Message{
// 		MessageSid:   *resp.Sid,
// 		Status:       *resp.Status,
// 		From:         *resp.From,
// 		To:           *resp.To,
// 		Content:      *resp.Body,
// 		ErrorMessage: *resp.ErrorMessage,
// 		ErrorCode:    *resp.ErrorCode,
// 	}, nil
// }

// todo remove this
func SendMessage(req SendMessageReq) (*Message, error) {
	// client := twilio.NewRestClientWithParams(twilio.ClientParams{
	// 	Username: req.Sid,
	// 	Password: req.Token,
	// })

	// params := &openapi.CreateMessageParams{}
	// params.SetTo(req.To)
	// // params.SetFrom(req.From)
	// params.SetBody(req.Content)
	// params.SetMessagingServiceSid(req.MessagingServiceSid)

	// resp, err := client.Api.CreateMessage(params)
	// if err != nil {
	// 	return nil, fmt.Errorf("send message err: %w", err)
	// }

	return &Message{
		MessageSid:   uid.GenerateUidWithPrefix("simulate-msgsid"),
		Status:       "simulate-status",
		From:         req.From,
		To:           req.To,
		Content:      req.Content,
		ErrorMessage: "",
		ErrorCode:    0,
	}, nil
}

func QueryMessage(sid, token, messageSid string) (*Message, error) {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: sid,
		Password: token,
	})

	resp, err := client.Api.FetchMessage(messageSid, nil)
	if err != nil {
		return nil, fmt.Errorf("query message err: %w", err)
	}

	return &Message{
		MessageSid:   *resp.Sid,
		Status:       *resp.Status,
		From:         *resp.From,
		To:           *resp.To,
		Content:      *resp.Body,
		ErrorMessage: *resp.ErrorMessage,
		ErrorCode:    *resp.ErrorCode,
	}, nil
}

type CreateMessagingServiceReq struct {
	SID   string
	Token string

	FriendlyName      string
	StatusCallback    string
	InboundRequestUrl string
}

type CreateMessagingServiceResp struct {
	Sid string
}

// func CreateMessagingService(req CreateMessagingServiceReq) (*CreateMessagingServiceResp, error) {
// 	client := twilio.NewRestClientWithParams(twilio.ClientParams{
// 		Username: req.SID,
// 		Password: req.Token,
// 	})

// 	params := &messaging.CreateServiceParams{}
// 	params.SetFriendlyName(req.FriendlyName)
// 	params.SetStatusCallback(req.StatusCallback)
// 	params.SetInboundRequestUrl(req.InboundRequestUrl)

// 	resp, err := client.MessagingV1.CreateService(params)
// 	if err != nil {
// 		return nil, fmt.Errorf("create messaging service err: %w", err)
// 	}

// 	return &CreateMessagingServiceResp{
// 		Sid: *resp.Sid,
// 	}, nil
// }

// todo remove this
func CreateMessagingService(req CreateMessagingServiceReq) (*CreateMessagingServiceResp, error) {
	// client := twilio.NewRestClientWithParams(twilio.ClientParams{
	// 	Username: req.SID,
	// 	Password: req.Token,
	// })

	// params := &messaging.CreateServiceParams{}
	// params.SetFriendlyName(req.FriendlyName)
	// params.SetStatusCallback(req.StatusCallback)
	// params.SetInboundRequestUrl(req.InboundRequestUrl)

	// resp, err := client.MessagingV1.CreateService(params)
	// if err != nil {
	// 	return nil, fmt.Errorf("create messaging service err: %w", err)
	// }

	return &CreateMessagingServiceResp{
		Sid: uid.GenerateUidWithPrefix("simulate-MessagingSid"),
	}, nil
}

// func AddNumberToMessagingService(sid, token, serviceSid, numberSid string) error {
// 	client := twilio.NewRestClientWithParams(twilio.ClientParams{
// 		Username: sid,
// 		Password: token,
// 	})

// 	params := &messaging.CreatePhoneNumberParams{}
// 	params.SetPhoneNumberSid(numberSid)

// 	_, err := client.MessagingV1.CreatePhoneNumber(serviceSid, params)
// 	return err
// }

// todo remove this
func AddNumberToMessagingService(sid, token, serviceSid, numberSid string) error {
	return nil
}
