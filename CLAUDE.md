# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Build and Run
- `make webapp` - Build and run the webapp (includes build, process management, and automatic restart)
- `make webapp-run` - Run the webapp (assumes binary is already built)
- `go run cmd/webapp/main.go` - Run directly from source (for development)

### Testing and Quality
- `make test` - Run all tests with race detection and coverage reporting
- `golangci-lint run ./...` - Run code quality checks (recommended for CI)

### Documentation
- `make swagger` - Generate Swagger documentation to api/webapp directory
- `make swagger-fmt` - Format Swagger comments
- `make swagger-serve` - Generate documentation and start server

### Build Management
- `make clean` - Remove build artifacts and coverage files
- `make help` - Show available commands

## Architecture Overview

This is a Go-based multi-tenant appointment booking system called "Pebble". The system follows a layered architecture pattern with clear separation of concerns.

### Core Concepts
- **Tenant**: Independent organization/customer with isolated data
- **Location**: Physical location/branch under a tenant with timezone settings
- **Account**: Internal system user belonging to a tenant/location
- **Client**: End customer who books appointments

### Application Structure
- `cmd/webapp/main.go` - Application entry point
- `internal/webapp/` - Core application logic
  - `config/` - Configuration loading and validation
  - `handler/` - HTTP request handlers (grouped by domain)
  - `models/` - Database models (GORM)
  - `services/` - Business logic layer
  - `types/` - API request/response structures
  - `router.go` - Route definitions
  - `server.go` - HTTP server setup and lifecycle

### Shared Libraries (`pkg/`)
- `uconfig/` - Viper-based configuration loading
- `udb/` - GORM database connection and health checks
- `ulog/` - Zap logger with Lumberjack rotation
- `ujwt/` - JWT token generation and validation
- `ugin/` - Gin framework extensions (middleware, validators, response writers)
- `uerror/` - Unified error handling
- `ubcrypt/` - Password hashing utilities
- `uid/` - KSUID generation for unique IDs
- `utwilio/` - Twilio SMS service integration

### Key Features
- JWT-based authentication with refresh tokens
- Multi-tenant data isolation
- Appointment scheduling and management
- Client management with tagging
- Staff management with role-based permissions
- Payment processing and refunds
- SMS messaging via Twilio
- Reporting and analytics
- Automated appointment reminders
- Health checks and monitoring endpoints

### Configuration
- Environment-specific configs in `configs/` directory
- Template file: `webapp_template.yaml`
- Configuration precedence: CLI args > env vars > config file > defaults
- Environment variables use `WEBAPP_` prefix (e.g., `WEBAPP_DATABASE_PASSWORD`)

### Database
- Uses GORM v2 with MySQL driver
- Models located in `internal/webapp/models/sqlserver/`
- Database connections configured via `pkg/udb`
- Health check available at `/health` endpoint

### API Structure
- Base path: `/api/v1`
- Authentication: JWT middleware on protected routes
- Documentation: Swagger UI at `/swagger/index.html`
- Internal worker endpoints: `/api/internal/workers` (protected by secret header)

### Development Workflow
1. Always read existing code in related directories to understand patterns
2. Follow Go conventions and existing code style
3. Use shared libraries from `pkg/` for common functionality
4. Add comprehensive error handling using `pkg/uerror`
5. Include proper logging using `pkg/ulog`
6. Test changes with `make test`
7. Generate/update Swagger docs when adding new endpoints

### Deployment
- Uses GitHub Actions for CI/CD (`.github/workflows/`)
- Deploy scripts in `scripts/webapp/`
- Automated health checks and monitoring
- Version injection during build process

### Code Style Guidelines
- Reference existing code in the same directory for consistency
- For new directories, follow patterns from sibling directories
- Use shared utilities and patterns from `pkg/`
- Follow Go naming conventions and formatting standards
- Add necessary comments for complex logic only